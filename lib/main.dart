import 'package:flutter/material.dart';
import 'package:video_editor_app/screens/home_screen.dart';
import 'package:video_editor_app/services/database_service.dart';
import 'package:video_editor_app/theme/neon_theme.dart';


void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize database
  await DatabaseService().database;
  
  runApp(const VideoEditorApp());
}

class VideoEditorApp extends StatelessWidget {
  const VideoEditorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Neon Video Editor',
      theme: NeonTheme.darkTheme,
      home: const HomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}