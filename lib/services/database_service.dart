import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart' as path;
import 'package:video_editor_app/models/video_project.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String dbPath = path.join(await getDatabasesPath(), 'video_editor.db');
    return await openDatabase(
      dbPath,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE projects(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        videoPath TEXT,
        audioPath TEXT,
        outputPath TEXT,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        filters TEXT,
        audioOffset REAL DEFAULT 0.0,
        videoDuration REAL DEFAULT 0.0,
        audioDuration REAL DEFAULT 0.0
      )
    ''');
  }

  Future<String> insertProject(VideoProject project) async {
    final db = await database;
    await db.insert(
      'projects',
      {
        ...project.toMap(),
        'filters': project.filters.toString(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    return project.id;
  }

  Future<List<VideoProject>> getAllProjects() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'projects',
      orderBy: 'updatedAt DESC',
    );

    return List.generate(maps.length, (i) {
      final map = Map<String, dynamic>.from(maps[i]);
      // Parse filters string back to Map
      if (map['filters'] != null && map['filters'].isNotEmpty) {
        try {
          // Simple parsing for basic filters - in production, use JSON
          map['filters'] = <String, dynamic>{};
        } catch (e) {
          map['filters'] = <String, dynamic>{};
        }
      } else {
        map['filters'] = <String, dynamic>{};
      }
      return VideoProject.fromMap(map);
    });
  }

  Future<VideoProject?> getProject(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'projects',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      final map = Map<String, dynamic>.from(maps.first);
      map['filters'] = <String, dynamic>{};
      return VideoProject.fromMap(map);
    }
    return null;
  }

  Future<void> updateProject(VideoProject project) async {
    final db = await database;
    await db.update(
      'projects',
      {
        ...project.toMap(),
        'filters': project.filters.toString(),
      },
      where: 'id = ?',
      whereArgs: [project.id],
    );
  }

  Future<void> deleteProject(String id) async {
    final db = await database;
    await db.delete(
      'projects',
      where: 'id = ?',
      whereArgs: [id],
    );
  }
}