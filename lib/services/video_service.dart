import 'dart:developer';
import 'dart:io';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:ffmpeg_kit_flutter_new/ffprobe_kit.dart';

import 'package:path_provider/path_provider.dart';
import 'package:video_editor_app/models/video_project.dart';

class VideoService {
  static final VideoService _instance = VideoService._internal();
  factory VideoService() => _instance;
  VideoService._internal();

  Future<String> mergeVideoAndAudio({
    required String videoPath,
    required String audioPath,
    required String outputFileName,
    double audioOffset = 0.0, // seconds; +ve = delay audio, -ve = advance audio
    VideoFilter? filter,
  }) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final outputPath = '${directory.path}/$outputFileName.mp4';

      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      // ---- Build video filter chain (string) ----
      String vf;
      if (filter != null) {
        final parts = <String>[];

        // Combine eq in one node
        final eq = <String>[];
        if (filter.brightness != 0.0) eq.add('brightness=${filter.brightness}');
        if (filter.contrast != 1.0) eq.add('contrast=${filter.contrast}');
        if (filter.saturation != 1.0) eq.add('saturation=${filter.saturation}');
        if (eq.isNotEmpty) parts.add('eq=${eq.join(':')}');

        if (filter.hue != 0.0) parts.add('hue=h=${filter.hue}');
        if (filter.blur > 0.0) parts.add('boxblur=${filter.blur}');

        // Stabilize colors/SAR at the end
        parts.add('format=yuv420p,setsar=1');

        vf = parts.join(',');
      } else {
        // Even without user filters, stabilize format to avoid runtime changes
        vf = 'format=yuv420p,setsar=1';
      }

      // ---- Build args ----
      final args = <String>[
        '-y',
        '-i', videoPath, // [0:v]
        '-i', audioPath, // [1:a]
      ];

      // Decide whether to use filter_complex (offset needs it)
      final usesFilterComplex = audioOffset != 0.0 || vf.isNotEmpty;

      if (usesFilterComplex) {
        final fc = <String>[];

        // Video branch: [0:v] -> (vf) -> [v]
        if (vf.isNotEmpty) {
          fc.add('[0:v]$vf[v]');
        } else {
          fc.add('[0:v]format=yuv420p,setsar=1[v]');
        }

        // Audio branch with offset -> [a]
        if (audioOffset > 0) {
          // Delay audio by N ms; pad so graph stays valid
          final ms = (audioOffset * 1000).round();
          fc.add('[1:a]adelay=$ms|$ms,apad,aresample=async=1[a]');
        } else if (audioOffset < 0) {
          // Advance audio: trim the first |offset| seconds
          final start = (-audioOffset).toStringAsFixed(6);
          fc.add('[1:a]atrim=start=$start,asetpts=PTS-STARTPTS,aresample=async=1[a]');
        } else {
          // No offset, just pass-through but keep a stable chain
          fc.add('[1:a]anull[a]');
        }

        args.addAll(['-filter_complex', fc.join(';'), '-map', '[v]', '-map', '[a]']);
      } else {
        // No offset: simple mapping + -vf
        args.addAll([
          '-map',
          '0:v:0',
          '-map',
          '1:a:0',
          if (vf.isNotEmpty) ...['-vf', vf],
        ]);
      }

      // Encoding & muxing settings
      args.addAll([
        '-c:v',
        'libx264',
        '-pix_fmt',
        'yuv420p',
        '-preset',
        'ultrafast',
        '-crf',
        '25',
        '-c:a',
        'aac',
        '-b:a',
        '128k',
        '-movflags',
        '+faststart',
        '-shortest',
        outputPath,
      ]);

      log('Executing FFmpeg (args): ${args.join(' ')}');

      final session = await FFmpegKit.executeWithArguments(args);
      final returnCode = await session.getReturnCode();

      if (ReturnCode.isSuccess(returnCode)) {
        if (await outputFile.exists()) {
          log('Video export successful: $outputPath');
          return outputPath;
        }
        throw Exception('Output file was not created');
      } else {
        final logs = await session.getLogs();
        final err = logs.map((l) => l.getMessage()).join('\n');
        throw Exception('Failed to merge video and audio: $err');
      }
    } catch (e) {
      log('Error processing video: $e');
      throw Exception('Error processing video: $e');
    }
  }

  Future<double> getVideoDuration(String videoPath) async {
    try {
      // Primary: FFprobe media info
      final session = await FFprobeKit.getMediaInformation(videoPath);
      final information = session.getMediaInformation();

      if (information != null) {
        final duration = information.getDuration();
        if (duration != null) {
          final parsed = double.tryParse(duration);
          if (parsed != null) return parsed;
        }
      }

      // Fallback: FFmpeg logs (args list)
      final ffmpegSession = await FFmpegKit.executeWithArguments([
        '-i', videoPath,
        '-f', 'null',
        '-', // write to null muxer
      ]);
      final logs = await ffmpegSession.getLogs();
      final output = logs.map((l) => l.getMessage()).join('\n');

      if (output.isNotEmpty) {
        final durationRegex = RegExp(r'Duration:\s+(\d{2}):(\d{2}):(\d{2})\.(\d{2})');
        final match = durationRegex.firstMatch(output);
        if (match != null) {
          final hours = int.parse(match.group(1)!);
          final minutes = int.parse(match.group(2)!);
          final seconds = int.parse(match.group(3)!);
          final centiseconds = int.parse(match.group(4)!);
          return hours * 3600 + minutes * 60 + seconds + centiseconds / 100.0;
        }
      }
      return 0.0;
    } catch (_) {
      return 0.0;
    }
  }

  Future<String> extractVideoThumbnail(String videoPath) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final thumbnailPath = '${directory.path}/thumbnail_${DateTime.now().millisecondsSinceEpoch}.jpg';

      // Seek BEFORE input for faster thumbnailing
      final args = <String>[
        '-y',
        '-ss', '1', // at 1s
        '-i', videoPath,
        '-frames:v', '1',
        thumbnailPath,
      ];
      final session = await FFmpegKit.executeWithArguments(args);
      final returnCode = await session.getReturnCode();

      if (ReturnCode.isSuccess(returnCode)) {
        return thumbnailPath;
      } else {
        final logs = await session.getLogs();
        final errorMessage = logs.map((l) => l.getMessage()).join('\n');
        throw Exception('Failed to extract thumbnail: $errorMessage');
      }
    } catch (e) {
      throw Exception('Error extracting thumbnail: $e');
    }
  }

  Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (_) {
      return false;
    }
  }
}
