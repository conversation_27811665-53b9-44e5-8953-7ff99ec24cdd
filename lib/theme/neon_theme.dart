import 'package:flutter/material.dart';

class NeonTheme {
  static const Color primaryNeon = Color(0xFF00FFFF); // Cyan
  static const Color secondaryNeon = Color(0xFFFF00FF); // Magenta
  static const Color accentNeon = Color(0xFF00FF00); // Green
  static const Color warningNeon = Color(0xFFFFFF00); // Yellow
  static const Color errorNeon = Color(0xFFFF0040); // Red-Pink
  
  static const Color darkBackground = Color(0xFF0A0A0A);
  static const Color cardBackground = Color(0xFF1A1A1A);
  static const Color surfaceColor = Color(0xFF2A2A2A);
  
  static ThemeData get darkTheme {
    return ThemeData(
      brightness: Brightness.dark,
      primaryColor: primaryNeon,
      scaffoldBackgroundColor: darkBackground,
      cardColor: cardBackground,
      
      colorScheme: const ColorScheme.dark(
        primary: primaryNeon,
        secondary: secondaryNeon,
        surface: surfaceColor,
        error: errorNeon,
      ),
      
      appBarTheme: AppBarTheme(
        backgroundColor: darkBackground,
        elevation: 0,
        titleTextStyle: TextStyle(
          color: primaryNeon,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          shadows: [
            Shadow(
              color: primaryNeon.withValues(alpha: 0.5),
              blurRadius: 10,
            ),
          ],
        ),
      ),
      
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryNeon.withValues(alpha: 0.2),
          foregroundColor: primaryNeon,
          side: BorderSide(color: primaryNeon, width: 1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      
      cardTheme: CardThemeData(
        color: cardBackground,
        elevation: 8,
        shadowColor: primaryNeon.withValues(alpha: 0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: primaryNeon.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
      
      textTheme: TextTheme(
        headlineLarge: TextStyle(
          color: primaryNeon,
          fontWeight: FontWeight.bold,
          shadows: [
            Shadow(
              color: primaryNeon.withValues(alpha: 0.5),
              blurRadius: 8,
            ),
          ],
        ),
        bodyLarge: const TextStyle(color: Colors.white),
        bodyMedium: TextStyle(color: Colors.white.withValues(alpha: 0.8)),
      ),
      
      iconTheme: IconThemeData(
        color: primaryNeon,
        shadows: [
          Shadow(
            color: primaryNeon.withValues(alpha: 0.5),
            blurRadius: 8,
          ),
        ],
      ),
    );
  }
  
  static BoxDecoration neonGlowContainer({
    Color? glowColor,
    double borderRadius = 12,
    double glowRadius = 8,
  }) {
    return BoxDecoration(
      color: cardBackground,
      borderRadius: BorderRadius.circular(borderRadius),
      border: Border.all(
        color: (glowColor ?? primaryNeon).withValues(alpha: 0.5),
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: (glowColor ?? primaryNeon).withValues(alpha: 0.3),
          blurRadius: glowRadius,
          spreadRadius: 2,
        ),
      ],
    );
  }
  
  static TextStyle neonTextStyle({
    Color? color,
    double fontSize = 16,
    FontWeight fontWeight = FontWeight.normal,
  }) {
    final neonColor = color ?? primaryNeon;
    return TextStyle(
      color: neonColor,
      fontSize: fontSize,
      fontWeight: fontWeight,
      shadows: [
        Shadow(
          color: neonColor.withValues(alpha: 0.5),
          blurRadius: 8,
        ),
      ],
    );
  }
}