import 'dart:io';
import 'package:flutter/material.dart';
import 'package:video_editor_app/models/video_project.dart';
import 'package:video_editor_app/services/database_service.dart';
import 'package:video_editor_app/theme/neon_theme.dart';
import 'package:video_editor_app/widgets/neon_button.dart';
import 'package:video_player/video_player.dart';

class GalleryScreen extends StatefulWidget {
  const GalleryScreen({super.key});

  @override
  State<GalleryScreen> createState() => _GalleryScreenState();
}

class _GalleryScreenState extends State<GalleryScreen> {
  final DatabaseService _databaseService = DatabaseService();
  List<VideoProject> _projects = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  Future<void> _loadProjects() async {
    try {
      final projects = await _databaseService.getAllProjects();
      setState(() {
        _projects = projects.where((p) => p.outputPath != null).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteProject(VideoProject project) async {
    try {
      // Delete the output file if it exists
      if (project.outputPath != null) {
        final file = File(project.outputPath!);
        if (await file.exists()) {
          await file.delete();
        }
      }

      await _databaseService.deleteProject(project.id);
      _loadProjects();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${project.name} deleted'),
          backgroundColor: NeonTheme.accentNeon,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error deleting project: $e'),
          backgroundColor: NeonTheme.errorNeon,
        ),
      );
    }
  }

  void _showDeleteDialog(VideoProject project) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: NeonTheme.cardBackground,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: NeonTheme.primaryNeon.withValues(alpha: 0.5)),
          ),
          title: Text(
            'Delete Project',
            style: NeonTheme.neonTextStyle(fontWeight: FontWeight.bold),
          ),
          content: Text(
            'Are you sure you want to delete "${project.name}"? This action cannot be undone.',
            style: const TextStyle(color: Colors.white),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: TextStyle(color: Colors.white.withValues(alpha: 0.7)),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteProject(project);
              },
              child: Text(
                'Delete',
                style: NeonTheme.neonTextStyle(color: NeonTheme.errorNeon),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Video Gallery',
          style: NeonTheme.neonTextStyle(fontWeight: FontWeight.bold),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [NeonTheme.darkBackground, NeonTheme.cardBackground],
          ),
        ),
        child: _isLoading
            ? const Center(
                child: CircularProgressIndicator(color: NeonTheme.primaryNeon),
              )
            : _projects.isEmpty
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.video_library_outlined,
                      size: 80,
                      color: NeonTheme.primaryNeon.withValues(alpha: 0.5),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'No exported videos yet',
                      style: NeonTheme.neonTextStyle(
                        fontSize: 20,
                        color: NeonTheme.primaryNeon.withValues(alpha: 0.7),
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Create and export your first video project',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.6),
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 30),
                    NeonButton(
                      text: 'CREATE PROJECT',
                      onPressed: () => Navigator.pop(context),
                      icon: Icons.add,
                    ),
                  ],
                ),
              )
            : GridView.builder(
                padding: const EdgeInsets.all(16),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 0.8,
                ),
                itemCount: _projects.length,
                itemBuilder: (context, index) {
                  final project = _projects[index];
                  return VideoThumbnailCard(
                    project: project,
                    onDelete: () => _showDeleteDialog(project),
                  );
                },
              ),
      ),
    );
  }
}

class VideoThumbnailCard extends StatefulWidget {
  final VideoProject project;
  final VoidCallback onDelete;

  const VideoThumbnailCard({
    super.key,
    required this.project,
    required this.onDelete,
  });

  @override
  State<VideoThumbnailCard> createState() => _VideoThumbnailCardState();
}

class _VideoThumbnailCardState extends State<VideoThumbnailCard> {
  VideoPlayerController? _controller;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  Future<void> _initializeVideo() async {
    if (widget.project.outputPath != null) {
      try {
        _controller = VideoPlayerController.file(
          File(widget.project.outputPath!),
        );
        await _controller!.initialize();
        setState(() {
          _isInitialized = true;
        });
      } catch (e) {
        // Handle error silently
      }
    }
  }

  void _playVideo() {
    if (_controller != null) {
      showDialog(
        context: context,
        builder: (context) => VideoPlayerDialog(controller: _controller!),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: NeonTheme.neonGlowContainer(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Video Thumbnail
          Expanded(
            child: GestureDetector(
              onTap: _playVideo,
              child: Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: _isInitialized && _controller != null
                      ? Stack(
                          children: [
                            AspectRatio(
                              aspectRatio: _controller!.value.aspectRatio,
                              child: VideoPlayer(_controller!),
                            ),
                            Center(
                              child: Container(
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: NeonTheme.primaryNeon.withValues(alpha: 0.8),
                                  boxShadow: [
                                    BoxShadow(
                                      color: NeonTheme.primaryNeon.withValues(
                                        alpha: 0.5,
                                      ),
                                      blurRadius: 10,
                                      spreadRadius: 2,
                                    ),
                                  ],
                                ),
                                child: const Icon(
                                  Icons.play_arrow,
                                  color: Colors.black,
                                  size: 32,
                                ),
                              ),
                            ),
                          ],
                        )
                      : Center(
                          child: Icon(
                            Icons.video_file,
                            size: 48,
                            color: NeonTheme.primaryNeon.withValues(alpha: 0.5),
                          ),
                        ),
                ),
              ),
            ),
          ),

          // Project Info
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.project.name,
                  style: NeonTheme.neonTextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  '${widget.project.updatedAt.day}/${widget.project.updatedAt.month}/${widget.project.updatedAt.year}',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.6),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: _playVideo,
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 6),
                          decoration: BoxDecoration(
                            color: NeonTheme.primaryNeon.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(
                              color: NeonTheme.primaryNeon.withValues(alpha: 0.5),
                            ),
                          ),
                          child: Center(
                            child: Text(
                              'PLAY',
                              style: NeonTheme.neonTextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: widget.onDelete,
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: NeonTheme.errorNeon.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                            color: NeonTheme.errorNeon.withValues(alpha: 0.5),
                          ),
                        ),
                        child: Icon(
                          Icons.delete_outline,
                          color: NeonTheme.errorNeon,
                          size: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class VideoPlayerDialog extends StatefulWidget {
  final VideoPlayerController controller;

  const VideoPlayerDialog({super.key, required this.controller});

  @override
  State<VideoPlayerDialog> createState() => _VideoPlayerDialogState();
}

class _VideoPlayerDialogState extends State<VideoPlayerDialog> {
  @override
  void initState() {
    super.initState();
    widget.controller.play();
  }

  @override
  void dispose() {
    widget.controller.pause();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        decoration: NeonTheme.neonGlowContainer(borderRadius: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Video Player',
                    style: NeonTheme.neonTextStyle(fontWeight: FontWeight.bold),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(Icons.close, color: NeonTheme.primaryNeon),
                  ),
                ],
              ),
            ),

            // Video Player
            AspectRatio(
              aspectRatio: widget.controller.value.aspectRatio,
              child: VideoPlayer(widget.controller),
            ),

            // Controls
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    onPressed: () {
                      if (widget.controller.value.isPlaying) {
                        widget.controller.pause();
                      } else {
                        widget.controller.play();
                      }
                      setState(() {});
                    },
                    icon: Icon(
                      widget.controller.value.isPlaying
                          ? Icons.pause
                          : Icons.play_arrow,
                      color: NeonTheme.primaryNeon,
                      size: 32,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
