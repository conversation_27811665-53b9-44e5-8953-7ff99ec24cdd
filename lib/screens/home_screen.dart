import 'package:flutter/material.dart';
import 'package:video_editor_app/models/video_project.dart';
import 'package:video_editor_app/screens/gallery_screen.dart';
import 'package:video_editor_app/screens/project_screen.dart';
import 'package:video_editor_app/services/database_service.dart';
import 'package:video_editor_app/theme/neon_theme.dart';
import 'package:video_editor_app/widgets/neon_button.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final DatabaseService _databaseService = DatabaseService();
  List<VideoProject> _recentProjects = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadRecentProjects();
  }

  Future<void> _loadRecentProjects() async {
    try {
      final projects = await _databaseService.getAllProjects();
      setState(() {
        _recentProjects = projects.take(3).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _createNewProject() async {
    final project = VideoProject(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: 'New Project_${DateTime.now().toIso8601String()}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _databaseService.insertProject(project);
    
    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ProjectScreen(project: project),
        ),
      ).then((_) => _loadRecentProjects());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              NeonTheme.darkBackground,
              NeonTheme.cardBackground,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'NEON',
                          style: NeonTheme.neonTextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Video Editor',
                          style: NeonTheme.neonTextStyle(
                            color: NeonTheme.secondaryNeon,
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                    IconButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const GalleryScreen(),
                          ),
                        );
                      },
                      icon: Icon(
                        Icons.photo_library,
                        color: NeonTheme.primaryNeon,
                        shadows: [
                          Shadow(
                            color: NeonTheme.primaryNeon.withValues(alpha: 0.5),
                            blurRadius: 8,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 40),
                
                // Create New Project Button
                Center(
                  child: NeonButton(
                    text: 'CREATE NEW PROJECT',
                    onPressed: _createNewProject,
                    width: double.infinity,
                    height: 60,
                  ),
                ),
                
                const SizedBox(height: 40),
                
                // Recent Projects
                Text(
                  'Recent Projects',
                  style: NeonTheme.neonTextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                const SizedBox(height: 20),
                
                Expanded(
                  child: _isLoading
                      ? const Center(
                          child: CircularProgressIndicator(
                            color: NeonTheme.primaryNeon,
                          ),
                        )
                      : _recentProjects.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.video_library_outlined,
                                    size: 64,
                                    color: NeonTheme.primaryNeon.withValues(alpha: 0.5),
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No projects yet',
                                    style: NeonTheme.neonTextStyle(
                                      color: NeonTheme.primaryNeon.withValues(alpha: 0.7),
                                      fontSize: 16,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Create your first video project',
                                    style: TextStyle(
                                      color: Colors.white.withValues(alpha: 0.6),
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : ListView.builder(
                              itemCount: _recentProjects.length,
                              itemBuilder: (context, index) {
                                final project = _recentProjects[index];
                                return Container(
                                  margin: const EdgeInsets.only(bottom: 16),
                                  decoration: NeonTheme.neonGlowContainer(),
                                  child: ListTile(
                                    contentPadding: const EdgeInsets.all(16),
                                    leading: Container(
                                      width: 60,
                                      height: 60,
                                      decoration: BoxDecoration(
                                        color: NeonTheme.primaryNeon.withValues(alpha: 0.2),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: NeonTheme.primaryNeon.withValues(alpha: 0.5),
                                        ),
                                      ),
                                      child: Icon(
                                        Icons.video_file,
                                        color: NeonTheme.primaryNeon,
                                      ),
                                    ),
                                    title: Text(
                                      project.name,
                                      style: NeonTheme.neonTextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Text(
                                      'Updated: ${project.updatedAt.day}/${project.updatedAt.month}/${project.updatedAt.year}',
                                      style: TextStyle(
                                        color: Colors.white.withValues(alpha: 0.6),
                                      ),
                                    ),
                                    trailing: Icon(
                                      Icons.arrow_forward_ios,
                                      color: NeonTheme.primaryNeon,
                                      size: 16,
                                    ),
                                    onTap: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => ProjectScreen(project: project),
                                        ),
                                      ).then((_) => _loadRecentProjects());
                                    },
                                  ),
                                );
                              },
                            ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}