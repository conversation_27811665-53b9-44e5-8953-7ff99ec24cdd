import 'dart:io';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:gal/gal.dart';
import '../models/video_project.dart';
import '../theme/neon_theme.dart';
import '../widgets/neon_button.dart';

class ExportPreviewScreen extends StatefulWidget {
  final VideoProject project;
  final String exportedVideoPath;

  const ExportPreviewScreen({
    super.key,
    required this.project,
    required this.exportedVideoPath,
  });

  @override
  State<ExportPreviewScreen> createState() => _ExportPreviewScreenState();
}

class _ExportPreviewScreenState extends State<ExportPreviewScreen> {
  VideoPlayerController? _controller;
  bool _isInitialized = false;
  bool _isPlaying = false;
  bool _isSaving = false;
  bool _isSaved = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  Future<void> _initializeVideo() async {
    try {
      _controller = VideoPlayerController.file(File(widget.exportedVideoPath));
      await _controller!.initialize();
      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      _showErrorSnackBar('Error loading video preview: $e');
    }
  }

  void _togglePlayPause() {
    if (_controller != null && _isInitialized) {
      setState(() {
        if (_isPlaying) {
          _controller!.pause();
          _isPlaying = false;
        } else {
          _controller!.play();
          _isPlaying = true;
        }
      });
    }
  }

  Future<void> _saveToGallery() async {
    if (_isSaving || _isSaved) return;

    setState(() {
      _isSaving = true;
    });

    try {
      // Check if the file exists
      final file = File(widget.exportedVideoPath);
      if (!await file.exists()) {
        throw Exception('Exported video file not found');
      }

      // Save to gallery using gal package
      await Gal.putVideo(widget.exportedVideoPath);
      
      setState(() {
        _isSaved = true;
        _isSaving = false;
      });
      
      _showSuccessSnackBar('Video saved to gallery successfully!');
    } catch (e) {
      setState(() {
        _isSaving = false;
      });
      _showErrorSnackBar('Failed to save video to gallery: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: NeonTheme.errorNeon,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: NeonTheme.primaryNeon,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              NeonTheme.darkBackground,
              NeonTheme.cardBackground,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: Icon(
                        Icons.arrow_back,
                        color: NeonTheme.primaryNeon,
                        shadows: [
                          Shadow(
                            color: NeonTheme.primaryNeon.withValues(alpha: 0.5),
                            blurRadius: 8,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Export Complete!',
                            style: NeonTheme.neonTextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            widget.project.name,
                            style: NeonTheme.neonTextStyle(
                              color: NeonTheme.secondaryNeon,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Video Preview
              Expanded(
                child: Container(
                  margin: const EdgeInsets.all(20),
                  decoration: NeonTheme.neonGlowContainer(),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: _isInitialized && _controller != null
                        ? Stack(
                            children: [
                              Center(
                                child: AspectRatio(
                                  aspectRatio: _controller!.value.aspectRatio,
                                  child: VideoPlayer(_controller!),
                                ),
                              ),
                              // Play/Pause overlay
                              Center(
                                child: GestureDetector(
                                  onTap: _togglePlayPause,
                                  child: Container(
                                    width: 80,
                                    height: 80,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: NeonTheme.primaryNeon.withValues(alpha: 0.8),
                                      boxShadow: [
                                        BoxShadow(
                                          color: NeonTheme.primaryNeon.withValues(alpha: 0.5),
                                          blurRadius: 20,
                                          spreadRadius: 5,
                                        ),
                                      ],
                                    ),
                                    child: Icon(
                                      _isPlaying ? Icons.pause : Icons.play_arrow,
                                      color: Colors.black,
                                      size: 40,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          )
                        : const Center(
                            child: CircularProgressIndicator(
                              color: NeonTheme.primaryNeon,
                            ),
                          ),
                  ),
                ),
              ),

              // Action Buttons
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    // Save to Gallery Button
                    NeonButton(
                      text: _isSaved
                          ? 'SAVED TO GALLERY ✓'
                          : _isSaving
                              ? 'SAVING...'
                              : 'SAVE TO GALLERY',
                      onPressed: _isSaved || _isSaving ? () {} : () => _saveToGallery(),
                      width: double.infinity,
                      height: 60,
                      icon: _isSaved
                          ? Icons.check_circle
                          : _isSaving
                              ? Icons.hourglass_empty
                              : Icons.save_alt,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Done Button
                    NeonButton(
                      text: 'DONE',
                      onPressed: () => Navigator.pop(context),
                      width: double.infinity,
                      height: 50,
                      icon: Icons.home,
                      glowColor: NeonTheme.secondaryNeon,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
