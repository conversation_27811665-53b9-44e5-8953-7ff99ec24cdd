import 'dart:developer';
import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:video_player/video_player.dart';
import 'package:just_audio/just_audio.dart';
import '../models/video_project.dart';
import '../services/database_service.dart';
import '../services/video_service.dart';
import '../theme/neon_theme.dart';
import '../screens/export_preview_screen.dart';

import '../widgets/enhanced_video_player.dart';
import '../widgets/scrollable_filter_controls.dart';

class ProjectScreen extends StatefulWidget {
  final VideoProject project;

  const ProjectScreen({super.key, required this.project});

  @override
  State<ProjectScreen> createState() => _ProjectScreenState();
}

class _ProjectScreenState extends State<ProjectScreen> with TickerProviderStateMixin {
  late VideoProject _currentProject;
  final DatabaseService _databaseService = DatabaseService();
  final VideoService _videoService = VideoService();
  final AudioPlayer _audioPlayer = AudioPlayer();
  
  VideoPlayerController? _videoController;
  bool _isProcessing = false;
  bool _isPlaying = false;
  VideoFilter _currentFilter = VideoFilter(name: 'Default');
  double _audioOffset = 0.0;
  
  // Animation controllers for smooth UI
  late AnimationController _controlsAnimationController;
  late Animation<double> _controlsAnimation;
  
  // Seeker position
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  bool _isDragging = false;

  // Sync timer for audio-video synchronization
  Timer? _syncTimer;

  @override
  void initState() {
    super.initState();
    _currentProject = widget.project;
    _initializeAnimations();
    _initializeControllers();
  }

  void _initializeAnimations() {
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _controlsAnimation = CurvedAnimation(
      parent: _controlsAnimationController,
      curve: Curves.easeInOut,
    );
    _controlsAnimationController.forward();
  }

  @override
  void dispose() {
    _syncTimer?.cancel();
    _videoController?.dispose();
    _audioPlayer.dispose();
    _controlsAnimationController.dispose();
    super.dispose();
  }

  Future<void> _initializeControllers() async {
    if (_currentProject.videoPath != null) {
      _videoController = VideoPlayerController.file(
        File(_currentProject.videoPath!),
      );
      await _videoController!.initialize();
      
      // Set up position listener for smooth seeking
      _videoController!.addListener(_videoListener);
      
      setState(() {
        _totalDuration = _videoController!.value.duration;
      });
    }
    
    if (_currentProject.audioPath != null) {
      try {
        await _audioPlayer.setFilePath(_currentProject.audioPath!);
      } catch (e) {
        debugPrint('Error loading audio: $e');
      }
    }
  }

  void _videoListener() {
    if (!_isDragging && _videoController != null && mounted) {
      final newPosition = _videoController!.value.position;
      final newIsPlaying = _videoController!.value.isPlaying;

      // Only update state if values have actually changed to reduce rebuilds
      if (_currentPosition != newPosition || _isPlaying != newIsPlaying) {
        setState(() {
          _currentPosition = newPosition;
          _isPlaying = newIsPlaying;
        });
      }
    }
  }

  Future<void> _pickVideo() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.video,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final videoPath = result.files.single.path!;
        final duration = await _videoService.getVideoDuration(videoPath);
        
        _currentProject = _currentProject.copyWith(
          videoPath: videoPath,
          videoDuration: duration,
          updatedAt: DateTime.now(),
        );
        
        await _databaseService.updateProject(_currentProject);
        await _initializeControllers();
      }
    } catch (e) {
      _showErrorSnackBar('Error picking video: $e');
    }
  }

  Future<void> _pickAudio() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final audioPath = result.files.single.path!;
        
        _currentProject = _currentProject.copyWith(
          audioPath: audioPath,
          updatedAt: DateTime.now(),
        );
        
        await _databaseService.updateProject(_currentProject);
        await _initializeControllers();
      }
    } catch (e) {
      _showErrorSnackBar('Error picking audio: $e');
    }
  }

  Future<void> _togglePlayPause() async {
    if (_videoController != null) {
      try {
        if (_isPlaying) {
          // Pause both simultaneously
          _stopSyncTimer();

          setState(() {
            _isPlaying = false;
          });

          await Future.wait([
            _videoController!.pause(),
            if (_currentProject.audioPath != null) _audioPlayer.pause(),
          ]);
        } else {
          // Start both simultaneously with proper sync
          setState(() {
            _isPlaying = true;
          });

          if (_currentProject.audioPath != null) {
            final videoPosition = _videoController!.value.position;
            final audioPosition = videoPosition + Duration(milliseconds: (_audioOffset * 1000).round());

            // Seek audio to correct position first
            await _audioPlayer.seek(audioPosition);

            // Start both at the same time
            await Future.wait([
              _videoController!.play(),
              _audioPlayer.play(),
            ]);

            // Start sync timer to keep audio and video in sync
            _startSyncTimer();
          } else {
            await _videoController!.play();
          }
        }
      } catch (e) {
        log('Error in _togglePlayPause: $e');
        // Reset state on error
        setState(() {
          _isPlaying = false;
        });
      }
    }
  }

  Future<void> _seekTo(Duration position) async {
    if (_videoController != null) {
      final wasPlaying = _isPlaying;

      // Pause both if playing
      if (wasPlaying) {
        await Future.wait([
          _videoController!.pause(),
          _audioPlayer.pause(),
        ]);
      }

      // Seek both to new position
      final audioPosition = position + Duration(milliseconds: (_audioOffset * 1000).round());

      await Future.wait([
        _videoController!.seekTo(position),
        if (_currentProject.audioPath != null) _audioPlayer.seek(audioPosition),
      ]);

      // Resume playing if it was playing before
      if (wasPlaying) {
        await Future.wait([
          _videoController!.play(),
          if (_currentProject.audioPath != null) _audioPlayer.play(),
        ]);
      }
    }
  }

  Future<void> _exportVideo() async {
    if (_currentProject.videoPath == null || _currentProject.audioPath == null) {
      _showErrorSnackBar('Please select both video and audio files');
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final outputPath = await _videoService.mergeVideoAndAudio(
        videoPath: _currentProject.videoPath!,
        audioPath: _currentProject.audioPath!,
        outputFileName: 'export_${DateTime.now().millisecondsSinceEpoch}',
        audioOffset: _audioOffset,
        filter: _currentFilter,
      );

      _currentProject = _currentProject.copyWith(
        outputPath: outputPath,
        updatedAt: DateTime.now(),
      );

      await _databaseService.updateProject(_currentProject);

      // Navigate to preview screen instead of showing success message
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ExportPreviewScreen(
              project: _currentProject,
              exportedVideoPath: outputPath,
            ),
          ),
        );
      }
    } catch (e) {
      _showErrorSnackBar('Export failed: $e');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: NeonTheme.errorNeon,
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NeonTheme.darkBackground,
      appBar: AppBar(
        title: Text(
          _currentProject.name,
          style: NeonTheme.neonTextStyle(fontWeight: FontWeight.bold),
        ),
        actions: [
          if (_isProcessing)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: NeonTheme.primaryNeon,
                  strokeWidth: 2,
                ),
              ),
            ),
        ],
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          // Calculate optimal video size maintaining 9:16 ratio
          final screenWidth = constraints.maxWidth;
          final screenHeight = constraints.maxHeight;

          // Calculate video dimensions with padding
          final availableWidth = screenWidth - 32; // 16px margin on each side
          final maxVideoHeight = screenHeight * 0.65; // Use 65% of screen height for video

          // Calculate dimensions maintaining 9:16 ratio
          double videoWidth = availableWidth;
          double videoHeight = videoWidth * (16 / 9);

          // If calculated height exceeds max height, adjust width
          if (videoHeight > maxVideoHeight) {
            videoHeight = maxVideoHeight;
            videoWidth = videoHeight * (9 / 16);
          }

          return Column(
            children: [
              // Video Player Section with optimized 9:16 ratio
              Expanded(
                flex: 5, // 50% of available space for video
                child: Container(
                  width: double.infinity,
                  margin: const EdgeInsets.all(16),
                  decoration: NeonTheme.neonGlowContainer(),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Center(
                      child: SizedBox(
                        width: videoWidth,
                        height: videoHeight,
                      child: _videoController != null && _videoController!.value.isInitialized
                          ? EnhancedVideoPlayer(
                              controller: _videoController!,
                              currentPosition: _currentPosition,
                              totalDuration: _totalDuration,
                              isPlaying: _isPlaying,
                              currentFilter: _currentFilter,
                              onPlayPause: _togglePlayPause,
                              onSeek: (position) {
                                setState(() {
                                  _isDragging = true;
                                  _currentPosition = position; // Update position immediately for smooth UI
                                });
                                _seekTo(position).then((_) {
                                  if (mounted) {
                                    setState(() {
                                      _isDragging = false;
                                    });
                                  }
                                });
                              },
                            )
                          : _buildVideoPlaceholder(),
                    ),
                  ),
                ),
              ),
            ),

          // Control Icons Row
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildControlIcon(
                  icon: Icons.video_library,
                  label: 'Video',
                  color: NeonTheme.primaryNeon,
                  onTap: _pickVideo,
                ),
                _buildControlIcon(
                  icon: Icons.audiotrack,
                  label: 'Audio',
                  color: NeonTheme.secondaryNeon,
                  onTap: _pickAudio,
                ),
                _buildControlIcon(
                  icon: _isPlaying ? Icons.pause : Icons.play_arrow,
                  label: _isPlaying ? 'Pause' : 'Play',
                  color: NeonTheme.accentNeon,
                  onTap: _togglePlayPause,
                ),
                _buildControlIcon(
                  icon: Icons.download,
                  label: 'Export',
                  color: NeonTheme.warningNeon,
                  onTap: _isProcessing ? null : _exportVideo,
                ),
              ],
            ),
          ),
          
          // Scrollable Controls Section
          Expanded(
            flex: 5, // 50% of available space for controls
            child: AnimatedBuilder(
              animation: _controlsAnimation,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, 50 * (1 - _controlsAnimation.value)),
                  child: Opacity(
                    opacity: _controlsAnimation.value,
                    child: ScrollableFilterControls(
                      filter: _currentFilter,
                      audioOffset: _audioOffset,
                      onFilterChanged: (filter) {
                        setState(() {
                          _currentFilter = filter;
                        });
                      },
                      onAudioOffsetChanged: (offset) {
                        setState(() {
                          _audioOffset = offset;
                        });
                      },
                    ),
                  ),
                );
              },
            ),
          ),
        ],
          );
        },
      ),
    );
  }

  Widget _buildVideoPlaceholder() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_file_outlined,
            size: 80,
            color: NeonTheme.primaryNeon.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No video selected',
            style: NeonTheme.neonTextStyle(
              color: NeonTheme.primaryNeon.withValues(alpha: 0.7),
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the video icon to select a video file',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlIcon({
    required IconData icon,
    required String label,
    required Color color,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.5),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.3),
              blurRadius: 8,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _startSyncTimer() {
    _stopSyncTimer();
    _syncTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (_videoController != null && _isPlaying && !_isDragging) {
        _checkAudioVideoSync();
      }
    });
  }

  void _stopSyncTimer() {
    _syncTimer?.cancel();
    _syncTimer = null;
  }

  void _checkAudioVideoSync() async {
    if (_videoController == null || _currentProject.audioPath == null) return;

    try {
      final videoPosition = _videoController!.value.position;
      final audioPosition = _audioPlayer.position;
      final expectedAudioPosition = videoPosition + Duration(milliseconds: (_audioOffset * 1000).round());

      // Check if audio and video are out of sync by more than 200ms
      final syncDifference = (audioPosition.inMilliseconds - expectedAudioPosition.inMilliseconds).abs();

      if (syncDifference > 200) {
        // Re-sync audio to video position
        await _audioPlayer.seek(expectedAudioPosition);
      }
    } catch (e) {
      // Ignore sync errors to prevent crashes
    }
  }
}