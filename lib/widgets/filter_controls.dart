import 'package:flutter/material.dart';
import 'package:video_editor_app/models/video_project.dart';
import 'package:video_editor_app/theme/neon_theme.dart';

class FilterControls extends StatefulWidget {
  final VideoFilter filter;
  final Function(VideoFilter) onFilterChanged;

  const FilterControls({
    super.key,
    required this.filter,
    required this.onFilterChanged,
  });

  @override
  State<FilterControls> createState() => _FilterControlsState();
}

class _FilterControlsState extends State<FilterControls> {
  late VideoFilter _currentFilter;

  @override
  void initState() {
    super.initState();
    _currentFilter = widget.filter;
  }

  void _updateFilter() {
    widget.onFilterChanged(_currentFilter);
  }

  Widget _buildSliderControl({
    required String label,
    required double value,
    required double min,
    required double max,
    required Function(double) onChanged,
    Color? color,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: NeonTheme.neonTextStyle(
                color: color ?? NeonTheme.primaryNeon,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              value.toStringAsFixed(2),
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: color ?? NeonTheme.primaryNeon,
            inactiveTrackColor: (color ?? NeonTheme.primaryNeon).withValues(
              alpha: 0.3,
            ),
            thumbColor: color ?? NeonTheme.primaryNeon,
            overlayColor: (color ?? NeonTheme.primaryNeon).withValues(
              alpha: 0.3,
            ),
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
            trackHeight: 4,
          ),
          child: Slider(
            value: value,
            min: min,
            max: max,
            divisions: 100,
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: NeonTheme.neonGlowContainer(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Video Filters',
                style: NeonTheme.neonTextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    _currentFilter = VideoFilter(name: 'Default');
                  });
                  _updateFilter();
                },
                child: Text(
                  'Reset',
                  style: NeonTheme.neonTextStyle(
                    color: NeonTheme.errorNeon,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Brightness Control
          _buildSliderControl(
            label: 'Brightness',
            value: _currentFilter.brightness,
            min: -1.0,
            max: 1.0,
            color: NeonTheme.warningNeon,
            onChanged: (value) {
              setState(() {
                _currentFilter = _currentFilter.copyWith(brightness: value);
              });
              _updateFilter();
            },
          ),

          const SizedBox(height: 16),

          // Contrast Control
          _buildSliderControl(
            label: 'Contrast',
            value: _currentFilter.contrast,
            min: 0.0,
            max: 3.0,
            color: NeonTheme.primaryNeon,
            onChanged: (value) {
              setState(() {
                _currentFilter = _currentFilter.copyWith(contrast: value);
              });
              _updateFilter();
            },
          ),

          const SizedBox(height: 16),

          // Saturation Control
          _buildSliderControl(
            label: 'Saturation',
            value: _currentFilter.saturation,
            min: 0.0,
            max: 3.0,
            color: NeonTheme.secondaryNeon,
            onChanged: (value) {
              setState(() {
                _currentFilter = _currentFilter.copyWith(saturation: value);
              });
              _updateFilter();
            },
          ),

          const SizedBox(height: 16),

          // Hue Control
          _buildSliderControl(
            label: 'Hue',
            value: _currentFilter.hue,
            min: -180.0,
            max: 180.0,
            color: NeonTheme.accentNeon,
            onChanged: (value) {
              setState(() {
                _currentFilter = _currentFilter.copyWith(hue: value);
              });
              _updateFilter();
            },
          ),

          const SizedBox(height: 16),

          // Blur Control
          _buildSliderControl(
            label: 'Blur',
            value: _currentFilter.blur,
            min: 0.0,
            max: 10.0,
            color: Colors.purple,
            onChanged: (value) {
              setState(() {
                _currentFilter = _currentFilter.copyWith(blur: value);
              });
              _updateFilter();
            },
          ),

          const SizedBox(height: 20),

          // Preset Filters
          Text(
            'Presets',
            style: NeonTheme.neonTextStyle(fontWeight: FontWeight.bold),
          ),

          const SizedBox(height: 12),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildPresetButton('Normal', VideoFilter(name: 'Normal')),
              _buildPresetButton(
                'Bright',
                VideoFilter(name: 'Bright', brightness: 0.3, contrast: 1.2),
              ),
              _buildPresetButton(
                'Vintage',
                VideoFilter(
                  name: 'Vintage',
                  brightness: -0.1,
                  contrast: 0.8,
                  saturation: 0.7,
                  hue: 15,
                ),
              ),
              _buildPresetButton(
                'Cool',
                VideoFilter(
                  name: 'Cool',
                  brightness: 0.1,
                  contrast: 1.1,
                  saturation: 1.2,
                  hue: -10,
                ),
              ),
              _buildPresetButton(
                'Dramatic',
                VideoFilter(
                  name: 'Dramatic',
                  brightness: -0.2,
                  contrast: 1.5,
                  saturation: 1.3,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPresetButton(String name, VideoFilter preset) {
    final isSelected = _currentFilter.name == name;

    return GestureDetector(
      onTap: () {
        setState(() {
          _currentFilter = preset;
        });
        _updateFilter();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected
              ? NeonTheme.primaryNeon.withValues(alpha: 0.3)
              : NeonTheme.primaryNeon.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? NeonTheme.primaryNeon
                : NeonTheme.primaryNeon.withValues(alpha: 0.5),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: NeonTheme.primaryNeon.withValues(alpha: 0.3),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ]
              : null,
        ),
        child: Text(
          name,
          style: TextStyle(
            color: isSelected ? NeonTheme.primaryNeon : Colors.white,
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
