import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/video_project.dart';
import '../theme/neon_theme.dart';

class ScrollableFilterControls extends StatefulWidget {
  final VideoFilter filter;
  final double audioOffset;
  final Function(VideoFilter) onFilterChanged;
  final Function(double) onAudioOffsetChanged;

  const ScrollableFilterControls({
    super.key,
    required this.filter,
    required this.audioOffset,
    required this.onFilterChanged,
    required this.onAudioOffsetChanged,
  });

  @override
  State<ScrollableFilterControls> createState() => _ScrollableFilterControlsState();
}

class _ScrollableFilterControlsState extends State<ScrollableFilterControls>
    with SingleTickerProviderStateMixin {
  late VideoFilter _currentFilter;
  late TabController _tabController;
  
  final List<Map<String, dynamic>> _tabs = [
    {'title': 'Sync', 'icon': Icons.sync},
    {'title': 'Filters', 'icon': Icons.tune},
    {'title': 'Presets', 'icon': Icons.palette},
  ];

  @override
  void initState() {
    super.initState();
    _currentFilter = widget.filter;
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _updateFilter() {
    widget.onFilterChanged(_currentFilter);
  }

  Widget _buildSliderControl({
    required String label,
    required double value,
    required double min,
    required double max,
    required Function(double) onChanged,
    Color? color,
    String? unit,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: NeonTheme.neonGlowContainer(
        glowColor: color ?? NeonTheme.primaryNeon,
        borderRadius: 12,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: NeonTheme.neonTextStyle(
                  color: color ?? NeonTheme.primaryNeon,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: (color ?? NeonTheme.primaryNeon).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: (color ?? NeonTheme.primaryNeon).withValues(alpha: 0.5),
                  ),
                ),
                child: Text(
                  '${value.toStringAsFixed(2)}${unit ?? ''}',
                  style: TextStyle(
                    color: color ?? NeonTheme.primaryNeon,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: color ?? NeonTheme.primaryNeon,
              inactiveTrackColor: (color ?? NeonTheme.primaryNeon).withValues(alpha: 0.2),
              thumbColor: color ?? NeonTheme.primaryNeon,
              overlayColor: (color ?? NeonTheme.primaryNeon).withValues(alpha: 0.3),
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 10),
              overlayShape: const RoundSliderOverlayShape(overlayRadius: 20),
              trackHeight: 6,
            ),
            child: Slider(
              value: value,
              min: min,
              max: max,
              divisions: ((max - min) * 100).round(),
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAudioSyncTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Audio Synchronization',
            style: NeonTheme.neonTextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Adjust the audio timing to sync with your video',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 20),
          
          _buildSliderControl(
            label: 'Audio Offset',
            value: widget.audioOffset,
            min: -10.0,
            max: 10.0,
            unit: 's',
            color: NeonTheme.secondaryNeon,
            onChanged: widget.onAudioOffsetChanged,
          ),
          
          // Quick sync buttons
          Container(
            padding: const EdgeInsets.all(16),
            decoration: NeonTheme.neonGlowContainer(
              glowColor: NeonTheme.accentNeon,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Quick Sync',
                  style: NeonTheme.neonTextStyle(
                    color: NeonTheme.accentNeon,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildQuickSyncButton('-1s', -1.0),
                    _buildQuickSyncButton('-0.5s', -0.5),
                    _buildQuickSyncButton('Reset', 0.0),
                    _buildQuickSyncButton('+0.5s', 0.5),
                    _buildQuickSyncButton('+1s', 1.0),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickSyncButton(String label, double offset) {
    final isActive = widget.audioOffset == offset;

    return GestureDetector(
      onTap: () {
        widget.onAudioOffsetChanged(offset);
        // Show feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Audio sync: ${offset}s'),
            duration: const Duration(milliseconds: 800),
            backgroundColor: NeonTheme.accentNeon,
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isActive
              ? NeonTheme.accentNeon.withValues(alpha: 0.4)
              : NeonTheme.accentNeon.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isActive
                ? NeonTheme.accentNeon
                : NeonTheme.accentNeon.withValues(alpha: 0.5),
            width: isActive ? 2 : 1,
          ),
          boxShadow: isActive ? [
            BoxShadow(
              color: NeonTheme.accentNeon.withValues(alpha: 0.3),
              blurRadius: 8,
              spreadRadius: 1,
            ),
          ] : null,
        ),
        child: Text(
          label,
          style: TextStyle(
            color: NeonTheme.accentNeon,
            fontSize: 12,
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildFiltersTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Video Filters',
                style: NeonTheme.neonTextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              GestureDetector(
                onTap: () {
                  setState(() {
                    _currentFilter = VideoFilter(name: 'Default');
                  });
                  _updateFilter();
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: NeonTheme.errorNeon.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: NeonTheme.errorNeon.withValues(alpha: 0.5),
                    ),
                  ),
                  child: Text(
                    'Reset All',
                    style: NeonTheme.neonTextStyle(
                      color: NeonTheme.errorNeon,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Brightness Control
          _buildSliderControl(
            label: 'Brightness',
            value: _currentFilter.brightness,
            min: -1.0,
            max: 1.0,
            color: NeonTheme.warningNeon,
            onChanged: (value) {
              setState(() {
                _currentFilter = _currentFilter.copyWith(brightness: value);
              });
              _updateFilter();

              // Provide haptic feedback
              if (value == 0.0) {
                // Reset to default
                HapticFeedback.lightImpact();
              }
            },
          ),
          
          // Contrast Control
          _buildSliderControl(
            label: 'Contrast',
            value: _currentFilter.contrast,
            min: 0.0,
            max: 3.0,
            color: NeonTheme.primaryNeon,
            onChanged: (value) {
              setState(() {
                _currentFilter = _currentFilter.copyWith(contrast: value);
              });
              _updateFilter();
            },
          ),
          
          // Saturation Control
          _buildSliderControl(
            label: 'Saturation',
            value: _currentFilter.saturation,
            min: 0.0,
            max: 3.0,
            color: NeonTheme.secondaryNeon,
            onChanged: (value) {
              setState(() {
                _currentFilter = _currentFilter.copyWith(saturation: value);
              });
              _updateFilter();
            },
          ),
          
          // Hue Control
          _buildSliderControl(
            label: 'Hue',
            value: _currentFilter.hue,
            min: -180.0,
            max: 180.0,
            unit: '°',
            color: NeonTheme.accentNeon,
            onChanged: (value) {
              setState(() {
                _currentFilter = _currentFilter.copyWith(hue: value);
              });
              _updateFilter();
            },
          ),
          
          // Blur Control
          _buildSliderControl(
            label: 'Blur',
            value: _currentFilter.blur,
            min: 0.0,
            max: 10.0,
            unit: 'px',
            color: Colors.purple,
            onChanged: (value) {
              setState(() {
                _currentFilter = _currentFilter.copyWith(blur: value);
              });
              _updateFilter();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPresetsTab() {
    final presets = [
      {'name': 'Normal', 'icon': Icons.clear, 'filter': VideoFilter(name: 'Normal')},
      {'name': 'Bright', 'icon': Icons.wb_sunny, 'filter': VideoFilter(name: 'Bright', brightness: 0.3, contrast: 1.2)},
      {'name': 'Vintage', 'icon': Icons.camera_alt, 'filter': VideoFilter(name: 'Vintage', brightness: -0.1, contrast: 0.8, saturation: 0.7, hue: 15)},
      {'name': 'Cool', 'icon': Icons.ac_unit, 'filter': VideoFilter(name: 'Cool', brightness: 0.1, contrast: 1.1, saturation: 1.2, hue: -10)},
      {'name': 'Dramatic', 'icon': Icons.flash_on, 'filter': VideoFilter(name: 'Dramatic', brightness: -0.2, contrast: 1.5, saturation: 1.3)},
      {'name': 'Warm', 'icon': Icons.local_fire_department, 'filter': VideoFilter(name: 'Warm', brightness: 0.1, contrast: 1.1, saturation: 1.1, hue: 20)},
      {'name': 'Cold', 'icon': Icons.snowing, 'filter': VideoFilter(name: 'Cold', brightness: 0.05, contrast: 1.2, saturation: 0.9, hue: -20)},
      {'name': 'High Contrast', 'icon': Icons.contrast, 'filter': VideoFilter(name: 'High Contrast', contrast: 2.0, saturation: 1.2)},
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filter Presets',
            style: NeonTheme.neonTextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose from professionally crafted filter presets',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 20),
          
          LayoutBuilder(
            builder: (context, constraints) {
              // Responsive grid based on available width
              final crossAxisCount = constraints.maxWidth > 400 ? 3 : 2;

              return GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: crossAxisCount,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: crossAxisCount == 3 ? 1.8 : 2.2,
                ),
                itemCount: presets.length,
                itemBuilder: (context, index) {
                  final preset = presets[index];
                  final isSelected = _currentFilter.name == preset['name'];

                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _currentFilter = preset['filter'] as VideoFilter;
                      });
                      _updateFilter();

                      // Show feedback
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Applied ${preset['name']} filter'),
                          duration: const Duration(milliseconds: 800),
                          backgroundColor: NeonTheme.primaryNeon,
                        ),
                      );
                    },
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? NeonTheme.primaryNeon.withValues(alpha: 0.3)
                            : NeonTheme.primaryNeon.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected
                              ? NeonTheme.primaryNeon
                              : NeonTheme.primaryNeon.withValues(alpha: 0.3),
                          width: isSelected ? 2 : 1,
                        ),
                        boxShadow: isSelected ? [
                          BoxShadow(
                            color: NeonTheme.primaryNeon.withValues(alpha: 0.4),
                            blurRadius: 12,
                            spreadRadius: 2,
                          ),
                        ] : null,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            preset['icon'] as IconData,
                            color: isSelected ? NeonTheme.primaryNeon : Colors.white,
                            size: crossAxisCount == 3 ? 20 : 24,
                          ),
                          const SizedBox(height: 6),
                          Text(
                            preset['name'] as String,
                            style: TextStyle(
                              color: isSelected ? NeonTheme.primaryNeon : Colors.white,
                              fontSize: crossAxisCount == 3 ? 11 : 12,
                              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: NeonTheme.cardBackground,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        border: Border.all(
          color: NeonTheme.primaryNeon.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          // Tab Bar
          Container(
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: NeonTheme.darkBackground,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: NeonTheme.primaryNeon.withValues(alpha: 0.3),
              ),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                color: NeonTheme.primaryNeon.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: NeonTheme.primaryNeon,
                ),
              ),
              labelColor: NeonTheme.primaryNeon,
              unselectedLabelColor: Colors.white.withValues(alpha: 0.6),
              labelStyle: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
              tabs: _tabs.map((tab) => Tab(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      tab['icon'] as IconData,
                      size: 16,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      tab['title'] as String,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              )).toList(),
            ),
          ),
          
          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAudioSyncTab(),
                _buildFiltersTab(),
                _buildPresetsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}