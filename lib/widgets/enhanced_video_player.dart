import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import '../theme/neon_theme.dart';
import '../models/video_project.dart';

class EnhancedVideoPlayer extends StatefulWidget {
  final VideoPlayerController controller;
  final Duration currentPosition;
  final Duration totalDuration;
  final bool isPlaying;
  final VoidCallback onPlayPause;
  final Function(Duration) onSeek;
  final VideoFilter? currentFilter;

  const EnhancedVideoPlayer({
    super.key,
    required this.controller,
    required this.currentPosition,
    required this.totalDuration,
    required this.isPlaying,
    required this.onPlayPause,
    required this.onSeek,
    this.currentFilter,
  });

  @override
  State<EnhancedVideoPlayer> createState() => _EnhancedVideoPlayerState();
}

class _EnhancedVideoPlayerState extends State<EnhancedVideoPlayer> {
  bool _showControls = true;
  double _seekerValue = 0.0;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _updateSeekerValue();
  }

  @override
  void didUpdateWidget(EnhancedVideoPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only update seeker if not dragging and position actually changed
    if (!_isDragging && oldWidget.currentPosition != widget.currentPosition) {
      _updateSeekerValue();
    }
  }

  void _updateSeekerValue() {
    if (widget.totalDuration.inMilliseconds > 0) {
      final newValue = widget.currentPosition.inMilliseconds / widget.totalDuration.inMilliseconds;
      // Only update if the value has changed significantly to reduce rebuilds
      if ((_seekerValue - newValue).abs() > 0.001) {
        _seekerValue = newValue;
      }
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  void _onSeekStart(double value) {
    setState(() {
      _isDragging = true;
      _seekerValue = value;
    });
  }

  void _onSeekUpdate(double value) {
    // Update seeker value immediately for smooth UI
    if (mounted) {
      setState(() {
        _seekerValue = value;
      });
    }
  }

  void _onSeekEnd(double value) {
    final position = Duration(
      milliseconds: (value * widget.totalDuration.inMilliseconds).round(),
    );

    // Update state immediately, then seek
    setState(() {
      _isDragging = false;
      _seekerValue = value;
    });

    // Perform seek operation
    widget.onSeek(position);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _showControls = !_showControls;
        });
      },
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: Stack(
          children: [
            // Video Player with proper aspect ratio handling
            Center(
              child: AspectRatio(
                aspectRatio: 9 / 16, // Maintain 9:16 ratio
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 10,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Stack(
                      children: [
                        VideoPlayer(widget.controller),
                        // Filter indicator overlay
                        if (widget.currentFilter != null && widget.currentFilter!.name != 'Default')
                          _buildFilterOverlay(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            
            // Controls Overlay
            if (_showControls)
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.7),
                      Colors.transparent,
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.8),
                    ],
                  ),
                ),
                child: Column(
                  children: [
                    // Top Controls
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: NeonTheme.primaryNeon.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: NeonTheme.primaryNeon.withValues(alpha: 0.5),
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.play_circle_outline,
                                  color: NeonTheme.primaryNeon,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'PREVIEW',
                                  style: NeonTheme.neonTextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: widget.isPlaying 
                                  ? NeonTheme.accentNeon.withValues(alpha: 0.2)
                                  : NeonTheme.errorNeon.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: widget.isPlaying 
                                    ? NeonTheme.accentNeon.withValues(alpha: 0.5)
                                    : NeonTheme.errorNeon.withValues(alpha: 0.5),
                              ),
                            ),
                            child: Text(
                              widget.isPlaying ? 'LIVE' : 'PAUSED',
                              style: TextStyle(
                                color: widget.isPlaying 
                                    ? NeonTheme.accentNeon
                                    : NeonTheme.errorNeon,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const Spacer(),
                    
                    // Center Play/Pause Button
                    Center(
                      child: GestureDetector(
                        onTap: widget.onPlayPause,
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: NeonTheme.primaryNeon.withValues(alpha: 0.2),
                            border: Border.all(
                              color: NeonTheme.primaryNeon,
                              width: 2,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: NeonTheme.primaryNeon.withValues(alpha: 0.5),
                                blurRadius: 20,
                                spreadRadius: 3,
                              ),
                            ],
                          ),
                          child: Icon(
                            widget.isPlaying ? Icons.pause : Icons.play_arrow,
                            color: NeonTheme.primaryNeon,
                            size: 40,
                          ),
                        ),
                      ),
                    ),
                    
                    const Spacer(),
                    
                    // Bottom Controls with Enhanced Seeker
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          // Time Display
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                _formatDuration(widget.currentPosition),
                                style: NeonTheme.neonTextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                _formatDuration(widget.totalDuration),
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.7),
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 8),
                          
                          // Enhanced Progress Bar
                          SizedBox(
                            height: 40,
                            child: SliderTheme(
                              data: SliderTheme.of(context).copyWith(
                                activeTrackColor: NeonTheme.primaryNeon,
                                inactiveTrackColor: Colors.white.withValues(alpha: 0.2),
                                thumbColor: NeonTheme.primaryNeon,
                                overlayColor: NeonTheme.primaryNeon.withValues(alpha: 0.3),
                                thumbShape: const RoundSliderThumbShape(
                                  enabledThumbRadius: 8,
                                ),
                                overlayShape: const RoundSliderOverlayShape(
                                  overlayRadius: 16,
                                ),
                                trackHeight: 4,
                              ),
                              child: Slider(
                                value: _seekerValue.clamp(0.0, 1.0),
                                min: 0.0,
                                max: 1.0,
                                onChangeStart: _onSeekStart,
                                onChanged: _onSeekUpdate,
                                onChangeEnd: _onSeekEnd,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            
            // Tap to show/hide controls indicator
            if (!_showControls)
              Center(
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.touch_app,
                    color: Colors.white.withValues(alpha: 0.7),
                    size: 24,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterOverlay() {
    if (widget.currentFilter == null) return const SizedBox.shrink();

    final filter = widget.currentFilter!;
    final hasActiveFilter = filter.brightness != 0.0 ||
                           filter.contrast != 1.0 ||
                           filter.saturation != 1.0 ||
                           filter.hue != 0.0 ||
                           filter.blur > 0.0;

    if (!hasActiveFilter) return const SizedBox.shrink();

    return Positioned(
      top: 8,
      right: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: NeonTheme.primaryNeon.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: NeonTheme.primaryNeon.withValues(alpha: 0.5),
              blurRadius: 8,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.auto_fix_high,
              color: Colors.black,
              size: 14,
            ),
            const SizedBox(width: 4),
            Text(
              filter.name,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}