class VideoProject {
  final String id;
  final String name;
  final String? videoPath;
  final String? audioPath;
  final String? outputPath;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic> filters;
  final double audioOffset;
  final double videoDuration;
  final double audioDuration;

  VideoProject({
    required this.id,
    required this.name,
    this.videoPath,
    this.audioPath,
    this.outputPath,
    required this.createdAt,
    required this.updatedAt,
    this.filters = const {},
    this.audioOffset = 0.0,
    this.videoDuration = 0.0,
    this.audioDuration = 0.0,
  });

  VideoProject copyWith({
    String? id,
    String? name,
    String? videoPath,
    String? audioPath,
    String? outputPath,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? filters,
    double? audioOffset,
    double? videoDuration,
    double? audioDuration,
  }) {
    return VideoProject(
      id: id ?? this.id,
      name: name ?? this.name,
      videoPath: videoPath ?? this.videoPath,
      audioPath: audioPath ?? this.audioPath,
      outputPath: outputPath ?? this.outputPath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      filters: filters ?? this.filters,
      audioOffset: audioOffset ?? this.audioOffset,
      videoDuration: videoDuration ?? this.videoDuration,
      audioDuration: audioDuration ?? this.audioDuration,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'videoPath': videoPath,
      'audioPath': audioPath,
      'outputPath': outputPath,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'filters': filters,
      'audioOffset': audioOffset,
      'videoDuration': videoDuration,
      'audioDuration': audioDuration,
    };
  }

  factory VideoProject.fromMap(Map<String, dynamic> map) {
    return VideoProject(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      videoPath: map['videoPath'],
      audioPath: map['audioPath'],
      outputPath: map['outputPath'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt'] ?? 0),
      filters: Map<String, dynamic>.from(map['filters'] ?? {}),
      audioOffset: map['audioOffset']?.toDouble() ?? 0.0,
      videoDuration: map['videoDuration']?.toDouble() ?? 0.0,
      audioDuration: map['audioDuration']?.toDouble() ?? 0.0,
    );
  }
}

class VideoFilter {
  final String name;
  final double brightness;
  final double contrast;
  final double saturation;
  final double hue;
  final double blur;

  VideoFilter({
    required this.name,
    this.brightness = 0.0,
    this.contrast = 1.0,
    this.saturation = 1.0,
    this.hue = 0.0,
    this.blur = 0.0,
  });

  VideoFilter copyWith({
    String? name,
    double? brightness,
    double? contrast,
    double? saturation,
    double? hue,
    double? blur,
  }) {
    return VideoFilter(
      name: name ?? this.name,
      brightness: brightness ?? this.brightness,
      contrast: contrast ?? this.contrast,
      saturation: saturation ?? this.saturation,
      hue: hue ?? this.hue,
      blur: blur ?? this.blur,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'brightness': brightness,
      'contrast': contrast,
      'saturation': saturation,
      'hue': hue,
      'blur': blur,
    };
  }

  factory VideoFilter.fromMap(Map<String, dynamic> map) {
    return VideoFilter(
      name: map['name'] ?? '',
      brightness: map['brightness']?.toDouble() ?? 0.0,
      contrast: map['contrast']?.toDouble() ?? 1.0,
      saturation: map['saturation']?.toDouble() ?? 1.0,
      hue: map['hue']?.toDouble() ?? 0.0,
      blur: map['blur']?.toDouble() ?? 0.0,
    );
  }
}