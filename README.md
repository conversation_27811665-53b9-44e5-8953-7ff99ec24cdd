# Neon Video Editor

A Flutter video editing app with a stunning neon glow theme that allows users to merge audio and video files with professional filters and effects.

## Features

### Core Functionality
- **Video & Audio Upload**: Select video files (without audio) and separate audio files
- **Real-time Preview**: Play video with synchronized audio before exporting
- **Audio Synchronization**: Fine-tune audio offset to perfectly sync with video
- **Video Filters**: Apply professional filters including:
  - Brightness adjustment
  - Contrast control
  - Saturation enhancement
  - Hue shifting
  - Blur effects
  - Preset filters (Normal, Bright, Vintage, Cool, Dramatic)

### User Interface
- **Neon Glow Theme**: Custom dark theme with cyan, magenta, and green neon accents
- **Animated Buttons**: Glowing buttons with pulsing animations
- **Modern Design**: Clean, professional interface optimized for mobile

### Project Management
- **Local Storage**: All projects saved locally on device
- **Video Gallery**: Browse and manage exported videos
- **Project History**: Access recent projects from home screen

### Export & Storage
- **High-Quality Export**: Merge video and audio with applied filters
- **Local Gallery**: Save exported videos to app's local gallery
- **Project Persistence**: Resume work on saved projects

## Technical Stack

- **Framework**: Flutter 3.35.4 (managed with FVM)
- **Video Processing**: FFmpeg Kit Flutter
- **Video Playback**: Video Player & Chewie
- **Audio Playback**: Just Audio
- **File Management**: File Picker & Path Provider
- **Database**: SQLite (sqflite)
- **State Management**: Provider
- **Permissions**: Permission Handler

## Installation

1. Ensure Flutter 3.35.4 is installed via FVM:
   ```bash
   fvm use 3.35.4
   ```

2. Install dependencies:
   ```bash
   fvm flutter pub get
   ```

3. Run the app:
   ```bash
   fvm flutter run
   ```

## Platform Support

- ✅ iOS (iPhone/iPad)
- ✅ Android (Phone/Tablet)

## Permissions

### iOS
- Photo Library Access
- Camera Access
- Microphone Access
- Documents Folder Access

### Android
- External Storage Read/Write
- Camera Access
- Audio Recording
- Internet Access
- Wake Lock

## Usage

1. **Create New Project**: Tap "CREATE NEW PROJECT" on home screen
2. **Select Files**: Choose video file and audio file using the selection buttons
3. **Preview**: Use the preview controls to play video with audio
4. **Sync Audio**: Adjust audio offset slider to synchronize audio with video
5. **Apply Filters**: Use filter controls to enhance video appearance
6. **Export**: Tap "EXPORT" to merge and save the final video
7. **Gallery**: Access exported videos from the gallery screen

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── models/
│   └── video_project.dart    # Data models
├── screens/
│   ├── home_screen.dart      # Main dashboard
│   ├── project_screen.dart   # Video editing interface
│   └── gallery_screen.dart   # Exported videos gallery
├── services/
│   ├── database_service.dart # SQLite operations
│   └── video_service.dart    # FFmpeg video processing
├── theme/
│   └── neon_theme.dart       # Custom neon theme
└── widgets/
    ├── neon_button.dart      # Animated glow buttons
    ├── video_player_widget.dart # Custom video player
    └── filter_controls.dart  # Video filter interface
```

## Development

This app uses FVM (Flutter Version Management) to ensure consistent Flutter version across development environments. Make sure to use Flutter 3.35.4 for compatibility.

### Key Dependencies
- `ffmpeg_kit_flutter`: Video processing and merging
- `video_player`: Video playback functionality
- `just_audio`: Audio playback and synchronization
- `file_picker`: File selection interface
- `sqflite`: Local database storage
- `permission_handler`: Runtime permissions

## Future Enhancements

- Video trimming and cutting
- More advanced filters and effects
- Social sharing capabilities
- Cloud storage integration
- Batch processing
- Video transitions and overlays