// This is a basic Flutter widget test for the Video Editor App.
import 'package:flutter_test/flutter_test.dart';
import 'package:video_editor_app/main.dart';

void main() {
  testWidgets('Video Editor App smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const VideoEditorApp());

    // Verify that our app loads with the home screen
    expect(find.text('NEON'), findsOneWidget);
    expect(find.text('Video Editor'), findsOneWidget);
    expect(find.text('CREATE NEW PROJECT'), findsOneWidget);
  });
}
